/**
 * Role Custom Admin Settings CSS
 * WordPress ayarlar sayfası için stil dosyası
 */

/* Ayarlar sayfası genel stil */
.role-custom-settings-page {
    max-width: 800px;
}

/* <PERSON><PERSON> b<PERSON><PERSON> başlıkları */
.role-custom-settings-page h2 {
    color: #23282d;
    font-size: 1.3em;
    margin: 1.5em 0 0.5em 0;
    padding-bottom: 0.5em;
    border-bottom: 1px solid #e1e1e1;
}

/* Ayar açıklamaları */
.role-custom-settings-page .description {
    color: #666;
    font-style: italic;
    margin-top: 5px;
    line-height: 1.4;
}

/* Checkbox stil */
.role-custom-settings-page input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.1);
}

/* Checkbox label */
.role-custom-settings-page label {
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
}

/* <PERSON>rum kartları */
.role-custom-status-card {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 15px;
    margin: 15px 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.role-custom-status-card h4 {
    margin: 0 0 10px 0;
    color: #23282d;
    font-size: 1.1em;
}

.role-custom-status-card p {
    margin: 5px 0;
    line-height: 1.4;
}

/* Durum göstergeleri */
.status-enabled {
    color: #46b450;
    font-weight: 600;
}

.status-disabled {
    color: #dc3232;
    font-weight: 600;
}

.status-warning {
    color: #ffb900;
    font-weight: 600;
}

/* Kod blokları */
.role-custom-settings-page code {
    background: #f1f1f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

/* Bilgi kutuları */
.role-custom-info-box {
    background: #e7f3ff;
    border-left: 4px solid #0073aa;
    padding: 12px;
    margin: 15px 0;
}

.role-custom-info-box p {
    margin: 0;
    color: #0073aa;
}

/* Uyarı kutuları */
.role-custom-warning-box {
    background: #fff8e5;
    border-left: 4px solid #ffb900;
    padding: 12px;
    margin: 15px 0;
}

.role-custom-warning-box p {
    margin: 0;
    color: #996800;
}

/* Hata kutuları */
.role-custom-error-box {
    background: #ffeaea;
    border-left: 4px solid #dc3232;
    padding: 12px;
    margin: 15px 0;
}

.role-custom-error-box p {
    margin: 0;
    color: #dc3232;
}

/* Başarı kutuları */
.role-custom-success-box {
    background: #ecf7ed;
    border-left: 4px solid #46b450;
    padding: 12px;
    margin: 15px 0;
}

.role-custom-success-box p {
    margin: 0;
    color: #2e7d32;
}

/* Form tablosu düzenlemeleri */
.role-custom-settings-page .form-table th {
    width: 250px;
    padding: 15px 10px 15px 0;
    vertical-align: top;
}

.role-custom-settings-page .form-table td {
    padding: 15px 10px;
    vertical-align: top;
}

/* Kaydet butonu */
.role-custom-settings-page .submit {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e1e1e1;
}

/* Responsive tasarım */
@media (max-width: 768px) {
    .role-custom-settings-page .form-table th,
    .role-custom-settings-page .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .role-custom-settings-page .form-table th {
        border-bottom: none;
    }
    
    .role-custom-status-card {
        padding: 10px;
    }
}

/* WordPress admin notice override */
.role-custom-settings-page .notice {
    margin: 15px 0;
}

/* Ayar bölümü ayırıcı */
.role-custom-settings-page hr {
    margin: 30px 0;
    border: none;
    border-top: 1px solid #e1e1e1;
}

/* Ayar başlığı ikonu */
.role-custom-settings-page h1:before {
    content: "⚙️";
    margin-right: 10px;
}

/* Eğitim ayarları ikonu */
.role-custom-settings-page h2:before {
    content: "🎓";
    margin-right: 8px;
}

/* Mevcut durum ikonu */
.role-custom-settings-page h2:last-of-type:before {
    content: "📊";
    margin-right: 8px;
}
